import {
  AppBar,
  Dialog,
  IconButton,
  Toolbar,
  Box,
  Button,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Slide,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material"
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat"
import CloseIcon from "@mui/icons-material/Close"

import { useState, useEffect, forwardRef } from "react"
import { getFunctions, httpsCallable } from "firebase/functions"

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />
})

function EditAutomationDialog({
  openAutomationEditorDialog,
  setOpenAutomationEditorDialog,
  editAutomationData,
  setEditAutomationData,
}) {
  const functions = getFunctions()

  const getAutomationSectionFieldList = httpsCallable(
    functions,
    "getautomationsectionsfieldlist"
  )
  const createCollectionMap = httpsCallable(functions, "createcollectionmap")

  const [isLoading, setIsLoading] = useState(false)
  const [triggerValidFieldList, setTriggerValidFieldList] = useState([])
  const [targetValidFieldObject, setTargetValidFieldObject] = useState([])
  const [fieldMap, setFieldMap] = useState({})
  const [mapChangesMade, setMapChangesMade] = useState(false)

  useEffect(() => {
    const triggerGetProjectTypeList = async () => {
      setIsLoading(true)
      console.log(editAutomationData, "edit automation data")
      getAutomationSectionFieldList(editAutomationData).then((data) => {
        // console.log(data, "section data <--------")
        setFieldMap(editAutomationData.fieldMap)
        setTargetValidFieldObject(data.data.targetFieldTypeObject)
        setTriggerValidFieldList(data.data.triggerValidFieldList)
        setIsLoading(false)
      })
    }

    if (editAutomationData) {
      triggerGetProjectTypeList()
    }
  }, [openAutomationEditorDialog])

  const submitCollectionMap = async () => {
    const map = {
      triggerKey: editAutomationData.triggerKey,
      automationData: {
        enabled: editAutomationData.enabled,
        fieldMap: fieldMap,
        targetSectionSelector: editAutomationData.targetSectionSelector,
        triggerSectionSelector: editAutomationData.triggerSectionSelector,
        triggerFieldSelector: editAutomationData.triggerFieldSelector,
        automationTypeLabel: "Create Collection Item From Static Section",
        automationType: "createCollectionAutomations",
        triggerKey: editAutomationData.triggerKey,
        triggerType: "customTaskflowAutomations",
        projectTypeLabel: editAutomationData.projectTypeLabel,
        projectTypeId: editAutomationData.projectTypeId,
      },
    }

    console.log(map, "map")

    await createCollectionMap(map)
      .then(() => console.log("successful update"))
      .catch((err) => console.log(err, "error update"))
    setOpenAutomationEditorDialog(false)
    setIsLoading(true)
    setMapChangesMade(false)
  }

  const selectTargetField = (e, triggerField) => {
    const currentMap = { ...fieldMap }
    currentMap[triggerField] = e.target.value

    console.log(currentMap, "current map")

    setFieldMap(currentMap)
    setMapChangesMade(true)
  }

  return (
    <Dialog
      fullScreen
      open={openAutomationEditorDialog}
      onClose={() => {
        setOpenAutomationEditorDialog(false)
        setFieldMap({})
        setTargetValidFieldObject([])
        setTriggerValidFieldList([])
        setMapChangesMade(false)
      }}
      TransitionComponent={Transition}
    >
      <AppBar sx={{ position: "relative" }}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => {
              setEditAutomationData("")
              setFieldMap({})
              setTargetValidFieldObject([])
              setTriggerValidFieldList([])
              setOpenAutomationEditorDialog(false)
              setMapChangesMade(false)
            }}
          >
            <CloseIcon />
          </IconButton>
          <Typography variant="h3">Automation Builder</Typography>
        </Toolbar>
      </AppBar>
      <Box
        sx={{
          margin: "75px 200px",
          display: "flex",
          justifyContent: "center",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        {mapChangesMade ? (
          <Box
            sx={{
              width: "100%",
              marginTop: "50px",
              display: "flex",
              justifyContent: "right",
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={() => submitCollectionMap()}
            >
              Save Collection Map
            </Button>
          </Box>
        ) : (
          ""
        )}
        <Box
          sx={{
            background: "#F9f9f9",
            border: "1px solid #ddd",
            borderRadius: "5px",
            padding: "10px",
            marginTop: "20px",
            width: "100%",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>
                    <Typography variant="h5">Trigger Field Name</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="h5">Trigger Field Type</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="h5"></Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="h5">Mappable Target Fields</Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {triggerValidFieldList.map((triggerField) => (
                  <TableRow key={triggerField.fieldSelector}>
                    <TableCell>{triggerField.name}</TableCell>
                    <TableCell>{triggerField.customFieldType}</TableCell>
                    <TableCell>
                      {targetValidFieldObject[
                        triggerField.customFieldType
                      ][0] ? (
                        <TrendingFlatIcon />
                      ) : (
                        ""
                      )}
                    </TableCell>
                    <TableCell align="right">
                      {targetValidFieldObject[
                        triggerField.customFieldType
                      ][0] ? (
                        <FormControl>
                          <InputLabel id="target-select">
                            Select Target Field
                          </InputLabel>
                          <Select
                            labelId="target-select"
                            sx={{ width: 300 }}
                            onChange={(e) =>
                              selectTargetField(e, triggerField.fieldSelector)
                            }
                            value={
                              fieldMap[triggerField.fieldSelector]
                                ? fieldMap[triggerField.fieldSelector]
                                : ""
                            }
                            label="Select Target Field"
                          >
                            {fieldMap[triggerField.fieldSelector] ? (
                              <MenuItem value={null}>
                                <em>None</em>
                              </MenuItem>
                            ) : (
                              ""
                            )}
                            {targetValidFieldObject[
                              triggerField.customFieldType
                            ].map((collectionField, index) => (
                              <MenuItem
                                key={index}
                                value={collectionField.fieldSelector}
                              >
                                {collectionField.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      ) : (
                        <Typography>No Valid Target Fields</Typography>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
        </Box>
        {isLoading ? <CircularProgress sx={{ marginTop: "20px" }} /> : ""}
        {mapChangesMade ? (
          <Box
            sx={{
              width: "100%",
              marginTop: "20px",
              display: "flex",
              justifyContent: "right",
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={() => submitCollectionMap()}
            >
              Save Collection Map
            </Button>
          </Box>
        ) : ""}
      </Box>
    </Dialog>
  )
}

export default EditAutomationDialog
