import { But<PERSON>, <PERSON>ack, Typography, LinearProgress } from "@mui/material"
import { useState } from "react"
import { useQueryClient } from "@tanstack/react-query"
import AlertSnackbar from "@/components/AlertSnackBar"
import {
  useCheckFormChangeSubscriptionStatus,
  useDeleteFormChangeSubscription,
  useSetupFormChangeTracking
} from "./firebaseMethods"

export default function FormHistory() {
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })
  const queryClient = useQueryClient()

  // Check subscription status
  const {
    data: subscriptionStatus,
    isLoading: isCheckingSubscription,
    isError: subscriptionError
  } = useCheckFormChangeSubscriptionStatus()

  // Setup form change tracking mutation
  const { mutate: setupFormChangeTracking, isPending: isActivating } = useSetupFormChangeTracking()

  // Delete subscription mutation
  const { mutate: deleteSubscription, isPending: isDeleting } = useDeleteFormChangeSubscription()

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const url =
    "https://identity.filevine.com/Account/Login?ReturnUrl=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3D4a57a84e-0c3b-423c-bc1d-e51b6af2b7cc%26response_type%3Dcode%26scope%3Dopenid%2520profile%2520email%2520phone%2520tenant%2520offline_access%2520theme%2520roles%2520filevine.v2.api.*%2520fv.identity.user.write%2520fv.auth.tenant.read%2520fv.vitals.api.*%2520fv.data.assets.management.service.s3create%2520fv.api.gateway.metrics.read%26code_challenge%3Ds4dKYeVFpsRuorglN_028KA3c1BAZPnk4wmypc0xmqA%26code_challenge_method%3DS256%26state%3DOpenIdConnect.AuthenticationProperties%253D8nckdi_haVDVz4AfvK3blgY2P4G7-W4FJqhxvRRwK_QdTdGTE4wCJsEMyCbQ9qYwUlw23VR0K8NW-OiiLHnnEMFUjwHKFkhldwfaF78_OnEu3Iq7pzma-asSYwtFotFzDhXIV0XGPqIaPUz_x1ytfHlwVLTPyI3gc0kktg5vjvB7uZNlrZh2LMzDJbnNqPwBpaXeBbtzjw4bN6E_Pibi10NWh1P8QabpyfwfVaL1N27yY95pyJ0ehhUifnEYEySeqU2kkYXDetUfufQjlIkXSCpIhgE%26response_mode%3Dform_post%26nonce%3D638852000810135111.NTFiMjllYzQtYTE0MC00MTU3LTlhN2YtMmNmZmVhZmIzYjFjNDRjZDlhMmQtY2I2OS00OTU3LTgwY2MtMjhhZDBhYzRkYjk3%26acr_values%3Dtenant%253A7db6aa81-6e17-498d-bce6-0194dd4ad7e8%26redirect_uri%3Dhttps%253A%252F%252Frops.filevineapp.com%252Fsignin-oidc%26x-client-SKU%3DID_NET472%26x-client-ver%3D6.35.0.0&x-fv-correlation-id=f24a412c42f446b78f80a58c620ca3b7"

  const handleActivateFormHistory = () => {
    setupFormChangeTracking(undefined, {
      onSuccess: () => {
        setAlertInfo({
          open: true,
          message: "Form change tracking activated successfully",
          severity: "success"
        })
        // Invalidate subscription status to refetch
        queryClient.invalidateQueries(["formChangeSubscriptionStatus"])
      },
      onError: (error) => {
        console.error("Setup failed:", error)
        setAlertInfo({
          open: true,
          message: `Error activating form history: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      }
    })
  }

  const handleDeleteSubscription = () => {
    deleteSubscription(undefined, {
      onSuccess: () => {
        setAlertInfo({
          open: true,
          message: "Form change subscription deleted successfully",
          severity: "success"
        })
        // Invalidate subscription status to refetch
        queryClient.invalidateQueries(["formChangeSubscriptionStatus"])
      },
      onError: (error) => {
        console.error("Delete failed:", error)
        setAlertInfo({
          open: true,
          message: `Error deleting subscription: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      }
    })
  }

  // Handle subscription error
  if (subscriptionError) {
    return (
      <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
        <Typography variant="h6" sx={{ marginBottom: "30px", textAlign: "center", color: "error.main" }}>
          Error Loading Subscription Status
        </Typography>
        <Typography variant="body2" sx={{ marginBottom: "40px", textAlign: "center", maxWidth: "400px" }}>
          Unable to check your form change subscription status. Please try again later.
        </Typography>
        <AlertSnackbar
          open={alertInfo.open}
          message={alertInfo.message}
          severity={alertInfo.severity}
          onClose={handleAlertClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        />
      </Stack>
    )
  }

  // Determine if subscription exists
  const hasSubscription = subscriptionStatus?.hasSubscription || false

  return (
    <Stack direction="column" alignItems="center" sx={{ padding: "50px 0" }}>
      <Typography variant="h6" sx={{ marginBottom: "30px", textAlign: "center" }}>
        Form History Tracking
      </Typography>

      {isCheckingSubscription && (
        <LinearProgress sx={{ width: "100%", maxWidth: "400px", marginBottom: "20px" }} />
      )}

      <Typography variant="body2" sx={{ marginBottom: "40px", textAlign: "center", maxWidth: "400px" }}>
        {hasSubscription
          ? "Form change tracking is currently active for your organization. You can delete the subscription if you no longer need this feature."
          : "Enable tracking of form changes across your organization to monitor field modifications and updates."}
      </Typography>

      {hasSubscription ? (
        <Button
          variant="contained"
          color="error"
          onClick={handleDeleteSubscription}
          disabled={isDeleting || isCheckingSubscription}
        >
          {isDeleting ? "Deleting..." : "Delete Subscription"}
        </Button>
      ) : (
        <Button
          variant="contained"
          color="primary"
          onClick={handleActivateFormHistory}
          disabled={isActivating || isCheckingSubscription}
        >
          {isActivating ? "Activating..." : "Activate Form History"}
        </Button>
      )}

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}
