import { useState } from "react"
import { Typo<PERSON>, Stack, Chip } from "@mui/material"

const RenderFormFields = ({ formFields }) => {
  const [activeChips, setActiveChips] = useState({})

  const handleChipClick = (index) => {
    setActiveChips((prev) => ({
      ...prev,
      [index]: !prev[index]
    }))
  }

  if (!formFields || formFields.length === 0) {
    return (
      <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary", padding: "10px" }}>
        No fields found
      </Typography>
    )
  }

  return (
    <Stack direction="row" flexWrap="wrap" spacing={1} sx={{ padding: "10px" }}>
      {formFields.map((field, index) => {
        const isActive = activeChips[index] || false

        return (
          <Chip
            key={index}
            label={field.fieldKey || field.name || `Field ${index + 1}`}
            sx={{
              "&.MuiChip-root": {
                margin: "8px",
                width: "30%",
                backgroundColor: isActive ? "#0c2d5f" : "#ebebeb",
                color: isActive ? "white" : "#666666",
                fontWeight: isActive ? "bold" : "normal",
                textAlign: "center",
                height: "auto",
                "& .MuiChip-label": {
                  overflow: "visible",
                  textOverflow: "unset",
                  whiteSpace: "normal",
                  wordBreak: "break-word",
                  display: "block",
                  padding: "8px 12px",
                  lineHeight: 1.2
                }
              }
            }}
            onClick={() => handleChipClick(index)}
          />
        )
      })}
    </Stack>
  )
}

export default RenderFormFields
