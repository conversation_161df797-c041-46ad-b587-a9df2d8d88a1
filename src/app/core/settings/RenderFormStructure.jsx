import { Typography, Accordion, AccordionSummary, AccordionDetails, Stack } from "@mui/material"
import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import RenderFormFields from "./RenderFormFields"
import Fallback from "./Fallback"

const RenderFormStructure = ({ structure }) => {
  if (!structure) return null

  // Handle the expected structure format
  if (structure.sections && Array.isArray(structure.sections)) {
    return (
      <Stack spacing={1}>
        {structure.sections.map((section, index) => (
          <Accordion key={index} sx={{ backgroundColor: "#fafafa" }}>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`section-${index}-content`}
              id={`section-${index}-header`}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
                {section.name || section.sectionSelector || `Section ${index + 1}`}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              {section.error ? (
                <Typography variant="body2" sx={{ color: "warning.main", fontStyle: "italic" }}>
                  {section.error}
                </Typography>
              ) : (
                <RenderFormFields formFields={section.formFields} />
              )}
            </AccordionDetails>
          </Accordion>
        ))}
      </Stack>
    )
  }

  // Fallback for unexpected structure formats
  return <Fallback structure={structure} />
}

export default RenderFormStructure
